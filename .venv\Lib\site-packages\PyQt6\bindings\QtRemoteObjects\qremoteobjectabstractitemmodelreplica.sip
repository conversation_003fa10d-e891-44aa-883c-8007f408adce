// qremoteobjectabstractitemmodelreplica.sip generated by MetaSIP
//
// This file is part of the QtRemoteObjects Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>ENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QAbstractItemModelReplica : public QAbstractItemModel /NoDefaultCtors/
{
%TypeHeaderCode
#include <qremoteobjectabstractitemmodelreplica.h>
%End

public:
    virtual ~QAbstractItemModelReplica();
    QItemSelectionModel *selectionModel() const;
    virtual QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const;
    virtual bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole);
    virtual QModelIndex parent(const QModelIndex &index) const;
    virtual QModelIndex index(int row, int column, const QModelIndex &parent = QModelIndex()) const;
    virtual bool hasChildren(const QModelIndex &parent = QModelIndex()) const;
    virtual int rowCount(const QModelIndex &parent = QModelIndex()) const;
    virtual int columnCount(const QModelIndex &parent = QModelIndex()) const;
    virtual QVariant headerData(int section, Qt::Orientation orientation, int role) const;
    virtual Qt::ItemFlags flags(const QModelIndex &index) const;
    QList<int> availableRoles() const;
    virtual QHash<int, QByteArray> roleNames() const;
    bool isInitialized() const;
    bool hasData(const QModelIndex &index, int role) const;
    size_t rootCacheSize() const;
    void setRootCacheSize(size_t rootCacheSize);

signals:
    void initialized();
};

%End
