// qpushbutton.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPushButton : public QAbstractButton
{
%TypeHeaderCode
#include <qpushbutton.h>
%End

public:
    explicit QPushButton(QWidget *parent /TransferThis/ = 0);
    QPushButton(const QString &text, QWidget *parent /TransferThis/ = 0);
    QPushButton(const QIcon &icon, const QString &text, QWidget *parent /TransferThis/ = 0);
    virtual ~QPushButton();
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    bool autoDefault() const;
    void setAutoDefault(bool);
    bool isDefault() const;
    void setDefault(bool);
    void setMenu(QMenu *menu /KeepReference/);
    QMenu *menu() const;
    void setFlat(bool);
    bool isFlat() const;

public slots:
    void showMenu();

protected:
    virtual void initStyleOption(QStyleOptionButton *option) const;
    virtual bool event(QEvent *e) /ReleaseGIL/;
    virtual void paintEvent(QPaintEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void focusInEvent(QFocusEvent *);
    virtual void focusOutEvent(QFocusEvent *);
    virtual bool hitButton(const QPoint &pos) const;
    virtual void mouseMoveEvent(QMouseEvent *);
};
