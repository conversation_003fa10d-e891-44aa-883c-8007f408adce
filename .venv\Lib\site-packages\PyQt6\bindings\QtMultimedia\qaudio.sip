// qaudio.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)
// Deprecated (and aliased to QtAudio) in Qt v6.7 but retained for compatibility.
// We also ignore any methods that refer to QtAudio.

namespace QAudio
{
%TypeHeaderCode
#include <qaudio.h>
%End

    enum Error
    {
        NoError,
        OpenError,
        IOError,
        UnderrunError,
        FatalError,
    };

    enum State
    {
        ActiveState,
        SuspendedState,
        StoppedState,
        IdleState,
    };

    enum VolumeScale
    {
        LinearVolumeScale,
        CubicVolumeScale,
        LogarithmicVolumeScale,
        DecibelVolumeScale,
    };

    float convertVolume(float volume, QAudio::VolumeScale from, QAudio::VolumeScale to);
};

%End

%PostInitialisationCode
// Implement QtAudio as an alternative name to QAudio.

PyObject *qaudio = PyDict_GetItemString(sipModuleDict, sipName_QAudio);

if (qaudio && PyDict_SetItemString(sipModuleDict, "QtAudio", qaudio) == 0)
    Py_INCREF(qaudio);
%End
