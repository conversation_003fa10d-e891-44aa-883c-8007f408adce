// qaudiodecoder.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QAudioDecoder : public QObject
{
%TypeHeaderCode
#include <qaudiodecoder.h>
%End

public:
    enum Error
    {
        NoError,
        ResourceError,
        FormatError,
        AccessDeniedError,
        NotSupportedError,
    };

    explicit QAudioDecoder(QObject *parent /TransferThis/ = 0);
    virtual ~QAudioDecoder();
    bool isSupported() const;
    bool isDecoding() const;
    QUrl source() const;
    void setSource(const QUrl &fileName);
    QIODevice *sourceDevice() const;
    void setSourceDevice(QIODevice *device);
    QAudioDecoder::Error error() const;
    QString errorString() const;
    QAudioBuffer read() const /ReleaseGIL/;
    bool bufferAvailable() const;
    qint64 position() const;
    qint64 duration() const;
    QAudioFormat audioFormat() const;
    void setAudioFormat(const QAudioFormat &format);

public slots:
    void start();
    void stop();

signals:
    void bufferAvailableChanged(bool);
    void bufferReady();
    void finished();
    void isDecodingChanged(bool);
    void error(QAudioDecoder::Error error);
    void sourceChanged();
    void positionChanged(qint64 position);
    void durationChanged(qint64 duration);
    void formatChanged(const QAudioFormat &format);
};

%End
