// qmenu.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMenu : public QWidget
{
%TypeHeaderCode
#include <qmenu.h>
%End

public:
    explicit QMenu(QWidget *parent /TransferThis/ = 0);
    QMenu(const QString &title, QWidget *parent /TransferThis/ = 0);
    virtual ~QMenu();
%If (- Qt_6_3_0)
    void addAction(QAction *);
%End
%If (- Qt_6_3_0)
    QAction *addAction(const QString &text) /Transfer/;
%End
%If (- Qt_6_3_0)
    QAction *addAction(const QString &text, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/, const QKeySequence &shortcut = 0) /Transfer/;
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt6_qtwidgets_get_connection_parts(a1, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipRes = sipCpp->addAction(*a0, receiver, slot_signature.constData(), *a2);
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(1, a1);
        }
%End

%End
%If (- Qt_6_3_0)
    QAction *addAction(const QIcon &icon, const QString &text) /Transfer/;
%End
%If (- Qt_6_3_0)
    QAction *addAction(const QIcon &icon, const QString &text, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/, const QKeySequence &shortcut = 0) /Transfer/;
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt6_qtwidgets_get_connection_parts(a2, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipRes = sipCpp->addAction(*a0, *a1, receiver, slot_signature.constData(), *a3);
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(2, a2);
        }
%End

%End
    QAction *addMenu(QMenu *menu);
    QMenu *addMenu(const QString &title) /Transfer/;
    QMenu *addMenu(const QIcon &icon, const QString &title) /Transfer/;
    QAction *addSeparator() /Transfer/;
    QAction *insertMenu(QAction *before, QMenu *menu);
    QAction *insertSeparator(QAction *before) /Transfer/;
    void clear();
    void setTearOffEnabled(bool);
    bool isTearOffEnabled() const;
    bool isTearOffMenuVisible() const;
    void hideTearOffMenu();
    void setDefaultAction(QAction * /KeepReference/);
    QAction *defaultAction() const;
    void setActiveAction(QAction *act);
    QAction *activeAction() const;
    void popup(const QPoint &p, QAction *action = 0);
    QAction *exec() /PostHook=__pyQtPostEventLoopHook__,PreHook=__pyQtPreEventLoopHook__,ReleaseGIL/;
    QAction *exec(const QPoint &p, QAction *action = 0) /PostHook=__pyQtPostEventLoopHook__,PreHook=__pyQtPreEventLoopHook__,ReleaseGIL/;
    static QAction *exec(const QList<QAction *> &actions, const QPoint &pos, QAction *at = 0, QWidget *parent = 0) /PostHook=__pyQtPostEventLoopHook__,PreHook=__pyQtPreEventLoopHook__,ReleaseGIL/;
    virtual QSize sizeHint() const;
    QRect actionGeometry(QAction *) const;
    QAction *actionAt(const QPoint &) const;
    QAction *menuAction() const;
    QString title() const;
    void setTitle(const QString &title);
    QIcon icon() const;
    void setIcon(const QIcon &icon);
    void setNoReplayFor(QWidget *widget);

signals:
    void aboutToHide();
    void aboutToShow();
    void hovered(QAction *action);
    void triggered(QAction *action);

protected:
    int columnCount() const;
    virtual void initStyleOption(QStyleOptionMenuItem *option, const QAction *action) const;
    virtual void changeEvent(QEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual void wheelEvent(QWheelEvent *);
    virtual void enterEvent(QEnterEvent *);
    virtual void leaveEvent(QEvent *);
    virtual void hideEvent(QHideEvent *);
    virtual void paintEvent(QPaintEvent *);
    virtual void actionEvent(QActionEvent *);
    virtual void timerEvent(QTimerEvent *);
    virtual bool event(QEvent *);
    virtual bool focusNextPrevChild(bool next);

public:
    bool isEmpty() const;
    bool separatorsCollapsible() const;
    void setSeparatorsCollapsible(bool collapse);
    QAction *addSection(const QString &text) /Transfer/;
    QAction *addSection(const QIcon &icon, const QString &text) /Transfer/;
    QAction *insertSection(QAction *before, const QString &text) /Transfer/;
    QAction *insertSection(QAction *before, const QIcon &icon, const QString &text) /Transfer/;
    bool toolTipsVisible() const;
    void setToolTipsVisible(bool visible);
%If (macOS)
    void setAsDockMenu();
%End
    void showTearOffMenu();
    void showTearOffMenu(const QPoint &pos);
%If (Qt_6_3_0 -)
    static QMenu *menuInAction(const QAction *action);
%End
};
