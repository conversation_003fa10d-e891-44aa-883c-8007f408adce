// qlabel.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLabel : public QFrame
{
%TypeHeaderCode
#include <qlabel.h>
%End

public:
    QLabel(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    QLabel(const QString &text, QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QLabel();
    QString text() const;
    QPixmap pixmap() const;
    QPicture picture() const;
    QMovie *movie() const;
    Qt::TextFormat textFormat() const;
    void setTextFormat(Qt::TextFormat);
    Qt::Alignment alignment() const;
    void setAlignment(Qt::Alignment);
    void setWordWrap(bool on);
    bool wordWrap() const;
    int indent() const;
    void setIndent(int);
    int margin() const;
    void setMargin(int);
    bool hasScaledContents() const;
    void setScaledContents(bool);
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    void setBuddy(QWidget * /KeepReference/);
    QWidget *buddy() const;
    virtual int heightForWidth(int) const;
    bool openExternalLinks() const;
    void setTextInteractionFlags(Qt::TextInteractionFlags flags);
    Qt::TextInteractionFlags textInteractionFlags() const;
    void setOpenExternalLinks(bool open);

public slots:
    void clear();
    void setMovie(QMovie *movie /KeepReference/);
    void setNum(double /Constrained/);
    void setNum(int);
    void setPicture(const QPicture &);
    void setPixmap(const QPixmap &);
    void setText(const QString &);

signals:
    void linkActivated(const QString &link);
    void linkHovered(const QString &link);

protected:
    virtual bool event(QEvent *e);
    virtual void paintEvent(QPaintEvent *);
    virtual void changeEvent(QEvent *);
    virtual void keyPressEvent(QKeyEvent *ev);
    virtual void mousePressEvent(QMouseEvent *ev);
    virtual void mouseMoveEvent(QMouseEvent *ev);
    virtual void mouseReleaseEvent(QMouseEvent *ev);
    virtual void contextMenuEvent(QContextMenuEvent *ev);
    virtual void focusInEvent(QFocusEvent *ev);
    virtual void focusOutEvent(QFocusEvent *ev);
    virtual bool focusNextPrevChild(bool next);

public:
    void setSelection(int, int);
    bool hasSelectedText() const;
    QString selectedText() const;
    int selectionStart() const;
%If (Qt_6_1_0 -)
    SIP_PYCALLABLE resourceProvider() const /TypeHint="Callable[[QUrl], QVariant]"/;
%MethodCode
        if (sipCpp->resourceProvider())
            sipRes = sipGetUserObject((sipSimpleWrapper *)sipSelf);
        else
            sipRes = SIP_NULLPTR;
        
        if (!sipRes)
            sipRes = Py_None;
        
        Py_INCREF(sipRes);
%End

%End
%If (Qt_6_1_0 -)
    void setResourceProvider(SIP_PYCALLABLE provider /TypeHint="Callable[[QUrl], QVariant]"/);
%MethodCode
        // Remove any existing callable.
        Py_XDECREF(sipGetUserObject((sipSimpleWrapper *)sipSelf));
        
        if (a0 == Py_None)
        {
            sipSetUserObject((sipSimpleWrapper *)sipSelf, SIP_NULLPTR);
            sipCpp->setResourceProvider(SIP_NULLPTR);
        }
        else
        {
            // Save the callable so that resourceProvider() can return it.
            Py_INCREF(a0);
            sipSetUserObject((sipSimpleWrapper *)sipSelf, a0);
        
            Py_BEGIN_ALLOW_THREADS
        
            sipCpp->setResourceProvider([a0] (const QUrl &arg) {
                QUrl *arg_heap = new QUrl(arg);
                QVariant qv;
                int is_err = 1;
        
                SIP_BLOCK_THREADS
        
                PyObject *arg_obj = sipConvertFromNewType(arg_heap, sipType_QUrl, NULL);
        
                if (arg_obj)
                {
                    PyObject *res_obj = PyObject_CallFunctionObjArgs(a0, arg_obj, NULL);
        
                    Py_DECREF(arg_obj);
        
                    if (res_obj)
                    {
                        is_err = 0;
        
                        QVariant *res = reinterpret_cast<QVariant *>(
                                sipConvertToType(res_obj, sipType_QVariant, NULL, 0,
                                        NULL, &is_err));
        
                        if (!is_err)
                        {
                            qv = *res;
                            delete res;
                        }
                    }
                }
                else
                {
                    delete arg_heap;
                }
        
                if (is_err)
                {
                    pyqt6_qtwidgets_err_print();
                }
        
                SIP_UNBLOCK_THREADS
        
                return qv;
            });
        
            Py_END_ALLOW_THREADS
        }
%End

%End
};

%ModuleHeaderCode
// Imports from QtCore.
typedef void (*pyqt6_qtwidgets_err_print_t)();
extern pyqt6_qtwidgets_err_print_t pyqt6_qtwidgets_err_print;
%End

%ModuleCode
// Imports from QtCore.
pyqt6_qtwidgets_err_print_t pyqt6_qtwidgets_err_print;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt6_qtwidgets_err_print = (pyqt6_qtwidgets_err_print_t)sipImportSymbol("pyqt6_err_print");
Q_ASSERT(pyqt6_qtwidgets_err_print);
%End
