// qmediaformat.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QMediaFormat
{
%TypeHeaderCode
#include <qmediaformat.h>
%End

public:
    enum FileFormat
    {
        UnspecifiedFormat,
        WMV,
        AVI,
        Matroska,
        MPEG4,
        Ogg,
        QuickTime,
        WebM,
        Mpeg4Audio,
        AAC,
        WMA,
        MP3,
        FLAC,
        Wave,
    };

    enum class AudioCodec
    {
        Unspecified,
        MP3,
        AAC,
        AC3,
        EAC3,
        FLAC,
        DolbyTrueHD,
        Opus,
        Vorbis,
        Wave,
        WMA,
        ALAC,
    };

    enum class VideoCodec
    {
        Unspecified,
        MPEG1,
        MPEG2,
        MPEG4,
        H264,
        H265,
        VP8,
        VP9,
        AV1,
        Theora,
        WMV,
        MotionJPEG,
    };

    enum ConversionMode
    {
        Encode,
        Decode,
    };

    enum ResolveFlags
    {
        NoFlags,
        RequiresVideo,
    };

    QMediaFormat(QMediaFormat::FileFormat format = QMediaFormat::UnspecifiedFormat);
    QMediaFormat(const QMediaFormat &other);
    ~QMediaFormat();
    void swap(QMediaFormat &other /Constrained/);
    QMediaFormat::FileFormat fileFormat() const;
    void setFileFormat(QMediaFormat::FileFormat f);
    void setVideoCodec(QMediaFormat::VideoCodec codec);
    QMediaFormat::VideoCodec videoCodec() const;
    void setAudioCodec(QMediaFormat::AudioCodec codec);
    QMediaFormat::AudioCodec audioCodec() const;
    bool isSupported(QMediaFormat::ConversionMode mode) const;
    QMimeType mimeType() const;
    QList<QMediaFormat::FileFormat> supportedFileFormats(QMediaFormat::ConversionMode m);
    QList<QMediaFormat::VideoCodec> supportedVideoCodecs(QMediaFormat::ConversionMode m);
    QList<QMediaFormat::AudioCodec> supportedAudioCodecs(QMediaFormat::ConversionMode m);
    static QString fileFormatName(QMediaFormat::FileFormat c);
    static QString audioCodecName(QMediaFormat::AudioCodec c);
    static QString videoCodecName(QMediaFormat::VideoCodec c);
    static QString fileFormatDescription(QMediaFormat::FileFormat c);
    static QString audioCodecDescription(QMediaFormat::AudioCodec c);
    static QString videoCodecDescription(QMediaFormat::VideoCodec c);
    void resolveForEncoding(QMediaFormat::ResolveFlags flags);
    bool operator==(const QMediaFormat &other) const;
    bool operator!=(const QMediaFormat &other) const;
};

%End
