// qsgmaterial.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSGMaterial /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qsgmaterial.h>
%End

public:
    enum Flag /BaseType=Flag/
    {
        Blending,
        RequiresDeterminant,
        RequiresFullMatrixExceptTranslate,
        RequiresFullMatrix,
%If (Qt_6_3_0 -)
        NoBatching,
%End
        CustomCompileStep,
    };

    typedef QFlags<QSGMaterial::Flag> Flags;
    QSGMaterial();
    virtual ~QSGMaterial();
    virtual QSGMaterialType *type() const = 0;
    virtual QSGMaterialShader *createShader(QSGRendererInterface::RenderMode renderMode) const = 0 /Factory/;
    virtual int compare(const QSGMaterial *other) const;
    QSGMaterial::Flags flags() const;
    void setFlag(QSGMaterial::Flags flags, bool enabled = true);
%If (Qt_6_8_0 -)
    int viewCount() const;
%End

private:
    QSGMaterial(const QSGMaterial &);
};
