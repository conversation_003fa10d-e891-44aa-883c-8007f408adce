// qpen.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPen /TypeHintIn="Union[QPen, QColor]"/
{
%TypeHeaderCode
#include <qpen.h>
%End

%ConvertToTypeCode
// SIP doesn't support automatic type convertors so we explicitly allow a
// QColor to be used whenever a QPen is expected.

if (sipIsErr == NULL)
    return (sipCanConvertToType(sipPy, sipType_QPen, SIP_NO_CONVERTORS) ||
            sipCanConvertToType(sipPy, sipType_QColor, 0));

if (sipCanConvertToType(sipPy, sipType_QPen, SIP_NO_CONVERTORS))
{
    *sipCppPtr = reinterpret_cast<QPen *>(sipConvertToType(sipPy, sipType_QPen, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

    return 0;
}

int state;
QColor *c = reinterpret_cast<QColor *>(sipConvertToType(sipPy, sipType_QColor, 0, 0, &state, sipIsErr));

if (*sipIsErr)
{
    sipReleaseType(c, sipType_QColor, state);
    return 0;
}

*sipCppPtr = new QPen(*c);

sipReleaseType(c, sipType_QColor, state);

return sipGetState(sipTransferObj);
%End

public:
    QPen();
    QPen(Qt::PenStyle);
    QPen(const QBrush &brush, qreal width, Qt::PenStyle style = Qt::SolidLine, Qt::PenCapStyle cap = Qt::SquareCap, Qt::PenJoinStyle join = Qt::BevelJoin);
    QPen(const QPen &pen);
    QPen(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QPen>())
            sipCpp = new QPen(a0->value<QPen>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    ~QPen();
    Qt::PenStyle style() const;
    void setStyle(Qt::PenStyle);
    qreal widthF() const;
    void setWidthF(qreal width);
    int width() const;
    void setWidth(int width);
    QColor color() const;
    void setColor(const QColor &color);
    QBrush brush() const;
    void setBrush(const QBrush &brush);
    bool isSolid() const;
    Qt::PenCapStyle capStyle() const;
    void setCapStyle(Qt::PenCapStyle pcs);
    Qt::PenJoinStyle joinStyle() const;
    void setJoinStyle(Qt::PenJoinStyle pcs);
    QList<qreal> dashPattern() const;
    void setDashPattern(const QList<qreal> &pattern);
    qreal miterLimit() const;
    void setMiterLimit(qreal limit);
    bool operator==(const QPen &p) const;
    bool operator!=(const QPen &p) const;
    qreal dashOffset() const;
    void setDashOffset(qreal doffset);
    bool isCosmetic() const;
    void setCosmetic(bool cosmetic);
    void swap(QPen &other /Constrained/);
};

QDataStream &operator<<(QDataStream &, const QPen &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QPen & /Constrained/) /ReleaseGIL/;
%If (Qt_6_9_0 -)
bool operator!=(const QPen &lhs, const Qt::PenStyle &rhs);
%End
%If (Qt_6_9_0 -)
bool operator!=(const QPen &lhs, const QColor &rhs);
%End
%If (Qt_6_9_0 -)
bool operator!=(const QColor &lhs, const QPen &rhs);
%End
%If (Qt_6_9_0 -)
bool operator==(const QPen &lhs, const Qt::PenStyle &rhs);
%End
%If (Qt_6_9_0 -)
bool operator==(const QPen &lhs, const QColor &rhs);
%End
%If (Qt_6_9_0 -)
bool operator==(const QColor &lhs, const QPen &rhs);
%End
