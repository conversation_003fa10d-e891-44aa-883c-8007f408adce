// qkeysequence.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QKeySequence /TypeHintIn="Union[QKeySequence, QKeySequence.StandardKey, QString, int]"/
{
%TypeHeaderCode
#include <qkeysequence.h>
%End

%ConvertToTypeCode
// Allow a StandardKey, QString or an integer whenever a QKeySequence is
// expected.

if (sipIsErr == NULL)
{
    if (sipCanConvertToType(sipPy, sipType_QKeySequence, SIP_NO_CONVERTORS))
        return 1;

    PyErr_Clear();
    sipConvertToEnum(sipPy, sipType_QKeySequence_StandardKey);
    if (!PyErr_Occurred())
        return 1;

    if (sipCanConvertToType(sipPy, sipType_QString, 0))
        return 1;

    PyErr_Clear();
    PyLong_AsLong(sipPy);

    return !PyErr_Occurred();
}

if (sipCanConvertToType(sipPy, sipType_QKeySequence, SIP_NO_CONVERTORS))
{
    *sipCppPtr = reinterpret_cast<QKeySequence *>(sipConvertToType(sipPy, sipType_QKeySequence, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

    return 0;
}

PyErr_Clear();
int skey = sipConvertToEnum(sipPy, sipType_QKeySequence_StandardKey);

if (!PyErr_Occurred())
{
    *sipCppPtr = new QKeySequence(static_cast<QKeySequence::StandardKey>(skey));

    return sipGetState(sipTransferObj);
}

PyErr_Clear();

if (sipCanConvertToType(sipPy, sipType_QString, 0))
{
    int state;
    QString *qs = reinterpret_cast<QString *>(sipConvertToType(sipPy, sipType_QString, 0, 0, &state, sipIsErr));

    if (*sipIsErr)
    {
        sipReleaseType(qs, sipType_QString, state);
        return 0;
    }

    *sipCppPtr = new QKeySequence(*qs);

    sipReleaseType(qs, sipType_QString, state);

    return sipGetState(sipTransferObj);
}

int key = PyLong_AsLong(sipPy);

*sipCppPtr = new QKeySequence(key);

return sipGetState(sipTransferObj);
%End

%PickleCode
    sipRes = Py_BuildValue("iiii", sipCpp->operator[](0), sipCpp->operator[](1), sipCpp->operator[](2), sipCpp->operator[](3));
%End

public:
    enum SequenceFormat
    {
        NativeText,
        PortableText,
    };

    enum SequenceMatch
    {
        NoMatch,
        PartialMatch,
        ExactMatch,
    };

    enum StandardKey
    {
        UnknownKey,
        HelpContents,
        WhatsThis,
        Open,
        Close,
        Save,
        New,
        Delete,
        Cut,
        Copy,
        Paste,
        Undo,
        Redo,
        Back,
        Forward,
        Refresh,
        ZoomIn,
        ZoomOut,
        Print,
        AddTab,
        NextChild,
        PreviousChild,
        Find,
        FindNext,
        FindPrevious,
        Replace,
        SelectAll,
        Bold,
        Italic,
        Underline,
        MoveToNextChar,
        MoveToPreviousChar,
        MoveToNextWord,
        MoveToPreviousWord,
        MoveToNextLine,
        MoveToPreviousLine,
        MoveToNextPage,
        MoveToPreviousPage,
        MoveToStartOfLine,
        MoveToEndOfLine,
        MoveToStartOfBlock,
        MoveToEndOfBlock,
        MoveToStartOfDocument,
        MoveToEndOfDocument,
        SelectNextChar,
        SelectPreviousChar,
        SelectNextWord,
        SelectPreviousWord,
        SelectNextLine,
        SelectPreviousLine,
        SelectNextPage,
        SelectPreviousPage,
        SelectStartOfLine,
        SelectEndOfLine,
        SelectStartOfBlock,
        SelectEndOfBlock,
        SelectStartOfDocument,
        SelectEndOfDocument,
        DeleteStartOfWord,
        DeleteEndOfWord,
        DeleteEndOfLine,
        InsertParagraphSeparator,
        InsertLineSeparator,
        SaveAs,
        Preferences,
        Quit,
        FullScreen,
        Deselect,
        DeleteCompleteLine,
        Backspace,
        Cancel,
    };

    QKeySequence();
    QKeySequence(const QKeySequence &ks /Constrained/);
    QKeySequence(QKeySequence::StandardKey key);
    QKeySequence(const QString &key, QKeySequence::SequenceFormat format = QKeySequence::NativeText);
    QKeySequence(int k1, int key2 = 0, int key3 = 0, int key4 = 0);
    QKeySequence(QKeyCombination k1, QKeyCombination key2 = QKeyCombination::fromCombined(0), QKeyCombination key3 = QKeyCombination::fromCombined(0), QKeyCombination key4 = QKeyCombination::fromCombined(0));
    QKeySequence(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QKeySequence>())
            sipCpp = new QKeySequence(a0->value<QKeySequence>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    ~QKeySequence();
    int count() const /__len__/;
    bool isEmpty() const;
    QKeySequence::SequenceMatch matches(const QKeySequence &seq) const;
    static QKeySequence mnemonic(const QString &text);
    QKeyCombination operator[](int i) const;
%MethodCode
        Py_ssize_t idx = sipConvertFromSequenceIndex(a0, sipCpp->count());
        
        if (idx < 0)
            sipIsErr = 1;
        else
            sipRes = new QKeyCombination(sipCpp->operator[]((uint)idx));
%End

    bool operator==(const QKeySequence &other) const;
    bool operator!=(const QKeySequence &other) const;
    bool operator<(const QKeySequence &ks) const;
    bool operator>(const QKeySequence &other) const;
    bool operator<=(const QKeySequence &other) const;
    bool operator>=(const QKeySequence &other) const;
    bool isDetached() const;
    void swap(QKeySequence &other /Constrained/);
    QString toString(QKeySequence::SequenceFormat format = QKeySequence::PortableText) const;
    static QKeySequence fromString(const QString &str, QKeySequence::SequenceFormat format = QKeySequence::PortableText);
    static QList<QKeySequence> keyBindings(QKeySequence::StandardKey key);
    static QList<QKeySequence> listFromString(const QString &str, QKeySequence::SequenceFormat format = QKeySequence::PortableText);
    static QString listToString(const QList<QKeySequence> &list, QKeySequence::SequenceFormat format = QKeySequence::PortableText);
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

QDataStream &operator<<(QDataStream &in, const QKeySequence &ks) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &out, QKeySequence &ks /Constrained/) /ReleaseGIL/;
void qt_set_sequence_auto_mnemonic(bool b);
