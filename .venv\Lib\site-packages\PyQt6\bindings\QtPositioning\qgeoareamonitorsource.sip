// qgeoareamonitorsource.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QGeoAreaMonitorSource : public QObject
{
%TypeHeaderCode
#include <qgeoareamonitorsource.h>
%End

public:
    enum Error
    {
        AccessError,
        InsufficientPositionInfo,
        UnknownSourceError,
        NoError,
    };

    enum AreaMonitorFeature /BaseType=Flag/
    {
        PersistentAreaMonitorFeature,
        AnyAreaMonitorFeature,
    };

    typedef QFlags<QGeoAreaMonitorSource::AreaMonitorFeature> AreaMonitorFeatures;
    explicit QGeoAreaMonitorSource(QObject *parent /TransferThis/);
    virtual ~QGeoAreaMonitorSource();
    static QGeoAreaMonitorSource *createDefaultSource(QObject *parent /TransferThis/) /Factory/;
    static QGeoAreaMonitorSource *createSource(const QString &sourceName, QObject *parent /TransferThis/) /Factory/;
    static QStringList availableSources();
    virtual void setPositionInfoSource(QGeoPositionInfoSource *source /Transfer/);
    virtual QGeoPositionInfoSource *positionInfoSource() const;
    QString sourceName() const;
    virtual QGeoAreaMonitorSource::Error error() const = 0;
    virtual QGeoAreaMonitorSource::AreaMonitorFeatures supportedAreaMonitorFeatures() const = 0;
    virtual bool startMonitoring(const QGeoAreaMonitorInfo &monitor) = 0;
    virtual bool stopMonitoring(const QGeoAreaMonitorInfo &monitor) = 0;
    virtual bool requestUpdate(const QGeoAreaMonitorInfo &monitor, const char *signal) = 0;
    virtual QList<QGeoAreaMonitorInfo> activeMonitors() const = 0;
    virtual QList<QGeoAreaMonitorInfo> activeMonitors(const QGeoShape &lookupArea) const = 0;

signals:
    void areaEntered(const QGeoAreaMonitorInfo &monitor, const QGeoPositionInfo &update);
    void areaExited(const QGeoAreaMonitorInfo &monitor, const QGeoPositionInfo &update);
    void monitorExpired(const QGeoAreaMonitorInfo &monitor);
    void errorOccurred(QGeoAreaMonitorSource::Error error);

public:
    virtual bool setBackendProperty(const QString &name, const QVariant &value);
    virtual QVariant backendProperty(const QString &name) const;
};

%End
