import os
import sys
import datetime
import threading
import unicodedata
from typing import Any, Dict, List, Optional, Tuple
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QGridLayout, QLabel, QLineEdit,
                             QPushButton, QFileDialog, QMessageBox, QTextEdit,
                             QListWidget, QGroupBox, QFrame, QSplitter)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon

try:
    import pyodbc
except ImportError:
    app = QApplication(sys.argv)
    msg = QMessageBox()
    msg.setIcon(QMessageBox.Icon.Critical)
    msg.setWindowTitle("Erro de Dependência")
    msg.setText("A biblioteca 'pyodbc' não está instalada.\n\nExecute no seu terminal:\npip install pyodbc")
    msg.exec()
    sys.exit(1)


def find_access_driver() -> Optional[str]:
    """Tenta encontrar um driver ODBC do Access instalado no Windows."""
    candidates = [
        "Microsoft Access Driver (*.mdb, *.accdb)",
        "Microsoft Access Driver (*.mdb)",
        "Microsoft Access Driver (*.accdb)",
    ]
    available = [d for d in pyodbc.drivers()]
    for cand in candidates:
        if cand in available:
            return cand
    return None


def connect_mdb(db_path: str) -> pyodbc.Connection:
    driver = find_access_driver()
    if not driver:
        print("Erro: Nenhum driver ODBC do Microsoft Access foi encontrado no sistema.", file=sys.stderr)
        print("Instale o 'Microsoft Access Database Engine' (32 ou 64 bits conforme seu Python).", file=sys.stderr)
        sys.exit(2)
    conn_str = f"DRIVER={{{driver}}};DBQ={db_path};READONLY=TRUE;"
    try:
        return pyodbc.connect(conn_str)
    except pyodbc.Error as e:
        print(f"Erro ao conectar ao MDB: {e}", file=sys.stderr)
        sys.exit(3)


def normalize_name(name: str) -> str:
    """Normaliza um nome para ser um identificador SQL seguro."""
    if not name:
        return ""
    # NFD normaliza caracteres acentuados em caractere base + acento
    nfkd_form = unicodedata.normalize('NFKD', name)
    # Remove os acentos (combinadores)
    sem_acentos = "".join([c for c in nfkd_form if not unicodedata.combining(c)])
    # Substitui % por _porcentagem
    sem_porcentagem = sem_acentos.replace('%', '_porcentagem')
    # Substitui espaços por underscore e converte para minúsculas
    nome_sem_espacos = sem_porcentagem.replace(' ', '_').lower()
    # Remove múltiplos underscores consecutivos
    while '__' in nome_sem_espacos:
        nome_sem_espacos = nome_sem_espacos.replace('__', '_')
    # Remove caracteres especiais, mantendo apenas letras, números e underscore
    nome_limpo = ''.join(c if c.isalnum() or c == '_' else '_' for c in nome_sem_espacos)
    # Remove múltiplos underscores consecutivos novamente
    while '__' in nome_limpo:
        nome_limpo = nome_limpo.replace('__', '_')
    # Remove underscores no início e fim
    return nome_limpo.strip('_')


def quote_ident(name: str) -> str:
    """Normaliza o nome do identificador."""
    return normalize_name(name)


def is_quantidade_field(name: str) -> bool:
    """Retorna True se o nome do campo contiver 'quantidade' (case-insensitive, normalizado)."""
    if not name:
        return False
    n = normalize_name(name)
    return "quantidade" in n


def is_hora_field(name: str) -> bool:
    """Retorna True se o nome do campo contiver 'hora' ou 'horario' (case-insensitive, normalizado)."""
    if not name:
        return False
    n = normalize_name(name)
    return "hora" in n or "horario" in n


# Mapear tipos do Access/ODBC para PostgreSQL
ACCESS_TO_PG_TYPE: Dict[str, str] = {
    # Texto
    "VARCHAR": "varchar(100)",
    "LONGCHAR": "text",
    "TEXT": "text",
    "MEMO": "text",
    # Inteiros
    "BYTE": "numeric(10,2)",
    "SHORT": "numeric(10,2)",
    "INTEGER": "integer",
    "LONG": "numeric(10,2)",
    "COUNTER": "numeric(10,2)",  # autonumber; não marcamos como serial para manter portabilidade
    # Numéricos
    "NUMERIC": "numeric(10,2)",
    "DECIMAL": "numeric(10,2)",
    "CURRENCY": "numeric(10,2)",
    "DOUBLE": "numeric(10,2)",
    "SINGLE": "real",
    # Booleano
    "YESNO": "boolean",
    "BIT": "boolean",
    # Datas
    "DATETIME": "date",
    "DATE": "date",
    "TIME": "time",
    # Binário
    "BINARY": "bytea",
    "LONGBINARY": "bytea",
    "OLEOBJECT": "bytea",
    # GUID
    "GUID": "uuid",
}


def odbc_type_to_pg(type_name: Optional[str], column_size: Optional[int], decimal_digits: Optional[int]) -> str:
    t = (type_name or "").upper()
    base = ACCESS_TO_PG_TYPE.get(t)
    if base is None:
        # fallback genérico por família de tipo
        if t in {"CHAR", "NCHAR", "NVARCHAR"}:
            base = "varchar(100)"
        elif t in {"FLOAT", "DOUBLE", "REAL"}:
            base = "double precision" if t != "REAL" else "real"
        elif t in {"TINYINT", "SMALLINT"}:
            base = "numeric(10)" 
        elif t in {"INT", "INTEGER", "LONG"}:
            base = "integer" 
        elif t in {"BIGINT"}:
            base = "bigint"
        elif t in {"DECIMAL", "NUMERIC"}:
            base = "numeric"
        elif t in {"BIT", "BOOLEAN", "YESNO"}:
            base = "boolean"
        elif t in {"DATETIME", "TIMESTAMP", "DATE", "TIME"}:
            base = "datetime"
        else:
            base = "varchar(100)"  # último recurso
    return base


def get_tables(cursor: pyodbc.Cursor) -> List[str]:
    tables: List[str] = []
    for row in cursor.tables(tableType='TABLE'):
        name = row.table_name
        if not name:
            continue
        if name.startswith("MSys"):
            continue  # ignora tabelas de sistema do Access
        tables.append(name)
    return tables


def get_columns(cursor: pyodbc.Cursor, table: str) -> List[Dict[str, Any]]:
    cols: List[Dict[str, Any]] = []
    for col in cursor.columns(table=table):
        cols.append({
            "name": col.column_name,
            "type_name": getattr(col, 'type_name', None),
            "data_type": getattr(col, 'data_type', None),  # código ODBC (não usamos diretamente)
            "column_size": getattr(col, 'column_size', None),
            "decimal_digits": getattr(col, 'decimal_digits', None),
            "nullable": bool(getattr(col, 'nullable', True)),
            "remarks": getattr(col, 'remarks', None),
            "ordinal_position": getattr(col, 'ordinal_position', None),
        })
    # Ordena pela posição ordinal se disponível
    if cols and any(c.get('ordinal_position') is not None for c in cols):
        cols.sort(key=lambda c: (c.get('ordinal_position') is None, c.get('ordinal_position', 0)))
    return cols


def get_primary_keys(cursor: pyodbc.Cursor, table: str) -> List[str]:
    pks: List[str] = []
    try:
        for pk in cursor.primaryKeys(table=table):
            if pk.column_name:
                pks.append(pk.column_name)
    except Exception:
        pass
    return pks


def format_value(value: Any) -> str:
    if value is None:
        return "NULL"
    if isinstance(value, bool):
        return "TRUE" if value else "FALSE"
    if isinstance(value, (int, float)):
        # Cobre numeric simples; cuidado com NaN/inf
        if isinstance(value, float):
            if value != value:  # NaN
                return "NULL"
            if value == float('inf') or value == float('-inf'):
                return "NULL"
        return str(value)
    if isinstance(value, (datetime.date, datetime.datetime)):
        # Representa como literal de timestamp ISO
        return "'" + value.strftime('%Y-%m-%d %H:%M:%S') + "'"
    if isinstance(value, (bytes, bytearray, memoryview)):
        # Bytea em hex: '\xDEADBEEF'
        hexstr = bytes(value).hex()
        return "'\\x" + hexstr + "'::bytea"
    # String e outros
    s = str(value)
    s = s.replace("'", "''")
    return "'" + s + "'"


def clean_table_name(table_name: str) -> str:
    """
    Limpa o nome da tabela:
    1. Remove todas as ocorrências de 'cadastro_' (case-insensitive)
    2. Substitui espaços por underscores
    3. Remove underscores extras
    """
    # Converte para minúsculas para fazer a busca case-insensitive
    lower_name = table_name.lower()
    cleaned = table_name
    
    # Remove todas as ocorrências de 'cadastro_'
    while 'cadastro_' in lower_name:
        # Encontra a posição de 'cadastro_' no nome em minúsculas
        pos = lower_name.index('cadastro_')
        # Remove 'cadastro_' do nome original
        cleaned = cleaned[:pos] + cleaned[pos + len('cadastro_'):]
        # Atualiza a versão em minúsculas
        lower_name = cleaned.lower()
    
    # Remove 'cadastro' se estiver no início (sem o underline)
    if lower_name.startswith('cadastro'):
        cleaned = cleaned[8:]
        lower_name = lower_name[8:]
    
    # Substitui espaços por underscore
    cleaned = cleaned.replace(' ', '_')
    
    # Remove múltiplos underscores consecutivos e underscores nas extremidades
    while '__' in cleaned:
        cleaned = cleaned.replace('__', '_')
    return cleaned.strip('_')

def generate_create_table(table: str, columns: List[Dict[str, Any]], pks: List[str]) -> str:
    """Gera o comando CREATE TABLE, transformando o primeiro campo em serial PRIMARY KEY."""
    if not columns:
        return f"-- AVISO: Tabela '{normalize_name(table)}' sem colunas, CREATE TABLE não gerado.\n"

    parts: List[str] = []
    
    # Processa o primeiro campo como a chave primária serial
    first_col = columns[0]
    first_col_name = quote_ident(first_col["name"])
    parts.append(f"    {first_col_name} serial NOT NULL PRIMARY KEY")

    # Processa os campos restantes
    for col in columns[1:]:
        colname = quote_ident(col["name"])
        pgtype = odbc_type_to_pg(col.get("type_name"), col.get("column_size"), col.get("decimal_digits"))
        # Regras especiais para tipos baseados no nome do campo
        col_name = col.get("name", "")
        if is_quantidade_field(col_name):
            pgtype = "numeric(10,2)"
        elif is_hora_field(col_name):
            pgtype = "time"
        
        # Não especifica NULL ou NOT NULL, usando o padrão do banco
        parts.append(f"    {colname} {pgtype}")

    # Remove 'cadastro' do nome da tabela
    clean_table = clean_table_name(table)
    
    cols_sql = ",\n".join(parts)
    return f"CREATE TABLE {quote_ident(clean_table)} (\n{cols_sql}\n);\n"


def generate_inserts(cursor: pyodbc.Cursor, table: str, columns: List[Dict[str, Any]]) -> List[str]:
    """Gera os comandos INSERT, ignorando a primeira coluna (que se tornou serial)."""
    if len(columns) < 2:
        # Não há dados para inserir se só havia uma coluna (que virou serial)
        return []
    
    # Remove 'cadastro' do nome da tabela
    clean_table = clean_table_name(table)

    # Ignora a primeira coluna para o INSERT e para o SELECT
    cols_for_insert = columns[1:]
    original_col_names = [c["name"] for c in cols_for_insert]
    normalized_col_names_sql = ", ".join(quote_ident(n) for n in original_col_names)
    
    # Usar SELECT * é mais robusto contra palavras-chave do Access como 'COUNT'.
    # A lógica de pular a primeira coluna será feita ao processar o resultado.
    query = f"SELECT * FROM [{table}]"

    inserts: List[str] = []
    try:
        cursor.execute(query)
    except pyodbc.Error as e:
        raise RuntimeError(f"Falha ao ler dados da tabela {table}: {e}")

    row = cursor.fetchone()
    while row is not None:
        # Pula o primeiro valor da linha, pois a primeira coluna virou serial
        values_sql = ", ".join(format_value(value) for value in row[1:])
        col_names = [quote_ident(col["name"]) for col in columns[1:]]
        insert_sql = f"INSERT INTO {quote_ident(clean_table)} ({', '.join(col_names)}) VALUES ({values_sql});"
        inserts.append(insert_sql)
        row = cursor.fetchone()
    return inserts


class ConversionWorker(QThread):
    """Worker thread para executar a conversão sem bloquear a interface."""
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)  # success, message

    def __init__(self, db_path, output_path, selected_tables):
        super().__init__()
        self.db_path = db_path
        self.output_path = output_path
        self.selected_tables = selected_tables

    def run(self):
        try:
            self.log_signal.emit(f"Conectando ao banco de dados: {self.db_path}")
            conn = connect_mdb(self.db_path)
            cursor = conn.cursor()

            self.log_signal.emit(f"Iniciando conversão de {len(self.selected_tables)} tabelas...")

            with open(self.output_path, 'w', encoding='utf-8') as f:
                # Processa cada tabela selecionada
                for i, table in enumerate(self.selected_tables, 1):
                    self.log_signal.emit(f"Processando tabela {i}/{len(self.selected_tables)}: {table}")

                    # Obtém as colunas e chaves primárias
                    columns = get_columns(cursor, table)
                    pks = get_primary_keys(cursor, table)

                    # Gera o CREATE TABLE
                    create_sql = generate_create_table(table, columns, pks)
                    f.write(f"{create_sql};\n\n")

                    # Gera os INSERTs
                    insert_statements = generate_inserts(cursor, table, columns)
                    if insert_statements:
                        # Junta todos os INSERTs com quebras de linha
                        f.write('\n'.join(insert_statements))
                        f.write("\n\n")

            conn.close()
            self.log_signal.emit(f"Conversão concluída com sucesso!\nArquivo salvo em: {self.output_path}")
            self.finished_signal.emit(True, f"Conversão concluída!\nArquivo salvo em:\n{self.output_path}")

        except Exception as e:
            self.log_signal.emit(f"ERRO: {e}")
            self.finished_signal.emit(False, f"Ocorreu um erro:\n{e}")


class ConverterApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Conversor MDB/ACCDB para PostgreSQL")
        self.setGeometry(100, 100, 1024, 780)

        # Centraliza a janela na tela
        self.center_window()

        # Variáveis para armazenar caminhos e dados
        self.input_path = ""
        self.output_path = ""
        self.sql_filename = ""
        self.available_tables = []
        self.last_input_path = ""

        # Worker thread para conversão
        self.conversion_worker = None

        # Configura a interface
        self.setup_ui()
        self.apply_styles()

    def center_window(self):
        """Centraliza a janela na tela."""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def setup_ui(self):
        """Configura a interface do usuário."""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # Seção de seleção de arquivos
        file_group = QGroupBox("Seleção de Arquivos")
        file_layout = QGridLayout(file_group)
        file_layout.setSpacing(10)

        # Arquivo de entrada
        file_layout.addWidget(QLabel("Arquivo de Entrada (MDB/ACCDB):"), 0, 0, 1, 2)

        input_layout = QHBoxLayout()
        self.input_entry = QLineEdit()
        self.input_entry.setPlaceholderText("Selecione o arquivo de banco de dados...")
        input_layout.addWidget(self.input_entry)

        self.input_browse_btn = QPushButton("Procurar...")
        self.input_browse_btn.clicked.connect(self.select_input_file)
        input_layout.addWidget(self.input_browse_btn)

        file_layout.addLayout(input_layout, 1, 0, 1, 2)

        # Nome do arquivo SQL
        file_layout.addWidget(QLabel("Nome do Arquivo SQL:"), 2, 0, 1, 2)
        self.sql_filename_entry = QLineEdit()
        self.sql_filename_entry.setPlaceholderText("Nome do arquivo de saída...")
        self.sql_filename_entry.textChanged.connect(self.update_output_path)
        file_layout.addWidget(self.sql_filename_entry, 3, 0, 1, 2)

        # Arquivo de saída
        file_layout.addWidget(QLabel("Arquivo de Saída (.sql):"), 4, 0, 1, 2)

        self.output_entry = QLineEdit()
        self.output_entry.setPlaceholderText("Caminho do arquivo de saída...")
        self.output_entry.setReadOnly(True)
        file_layout.addWidget(self.output_entry, 5, 0, 1, 2)

        main_layout.addWidget(file_group)

        # Seção de seleção de tabelas
        table_group = QGroupBox("Tabelas para Converter")
        table_layout = QVBoxLayout(table_group)

        # Botão para carregar tabelas
        self.load_tables_btn = QPushButton("Carregar Tabelas")
        self.load_tables_btn.clicked.connect(self.load_tables)
        self.load_tables_btn.setEnabled(False)
        table_layout.addWidget(self.load_tables_btn)

        # Lista de tabelas
        self.table_listbox = QListWidget()
        self.table_listbox.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        table_layout.addWidget(self.table_listbox)

        main_layout.addWidget(table_group)

        # Botão de conversão
        self.convert_button = QPushButton("Iniciar Conversão")
        self.convert_button.clicked.connect(self.start_conversion)
        self.convert_button.setEnabled(False)
        main_layout.addWidget(self.convert_button)

        # Log da conversão
        log_group = QGroupBox("Log da Conversão")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)

        main_layout.addWidget(log_group)

    def apply_styles(self):
        """Aplica estilos modernos à interface."""
        # Estilo para o botão de conversão
        self.convert_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        # Estilo para botões de procurar
        button_style = """
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """

        self.input_browse_btn.setStyleSheet(button_style)
        self.load_tables_btn.setStyleSheet(button_style)

        # Estilo para campos de entrada
        entry_style = """
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #2196F3;
            }
        """

        self.input_entry.setStyleSheet(entry_style)
        self.output_entry.setStyleSheet(entry_style)
        self.sql_filename_entry.setStyleSheet(entry_style)

        # Estilo para a lista de tabelas
        self.table_listbox.setStyleSheet("""
            QListWidget {
                border: 2px solid #ddd;
                border-radius: 4px;
                padding: 4px;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 4px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #2196F3;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #e3f2fd;
            }
        """)

        # Estilo para o log
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                background-color: #f8f9fa;
            }
        """)

    def log(self, message):
        """Adiciona uma mensagem ao log."""
        self.log_text.append(message)
        self.log_text.ensureCursorVisible()

    def select_input_file(self):
        """Abre a caixa de diálogo para selecionar o arquivo de entrada."""
        filename, _ = QFileDialog.getOpenFileName(
            self,
            "Selecionar Arquivo de Banco de Dados",
            "",
            "Arquivos do Access (*.mdb *.accdb);;Todos os arquivos (*.*)"
        )
        if filename:
            self.input_path = filename
            self.input_entry.setText(filename)
            self.last_input_path = filename
            self.load_tables_btn.setEnabled(True)

            # Define um nome padrão para o arquivo de saída baseado no nome do arquivo de entrada
            base_name = os.path.splitext(os.path.basename(filename))[0]
            date_str = datetime.datetime.now().strftime("%d-%m-%Y")
            default_output = f"{base_name}_{date_str}.sql"
            output_dir = os.path.dirname(filename)
            self.output_path = os.path.join(output_dir, default_output)
            self.output_entry.setText(self.output_path)
            self.sql_filename = default_output
            self.sql_filename_entry.setText(default_output)



    def update_output_path(self):
        """Atualiza o caminho de saída quando o nome do arquivo é alterado."""
        if self.last_input_path:
            # Mantém o mesmo diretório do arquivo de entrada, apenas atualiza o nome do arquivo
            output_dir = os.path.dirname(self.last_input_path)
            filename = self.sql_filename_entry.text()
            if filename and not filename.lower().endswith('.sql'):
                filename += '.sql'
            if filename:
                self.output_path = os.path.join(output_dir, filename)
                self.output_entry.setText(self.output_path)
                self.sql_filename = filename

    def load_tables(self):
        """Carrega a lista de tabelas do banco de dados."""
        if not self.input_path or not os.path.exists(self.input_path):
            QMessageBox.critical(self, "Erro", "Arquivo de banco de dados não encontrado!")
            return

        try:
            conn = connect_mdb(self.input_path)
            cursor = conn.cursor()

            # Limpa a lista atual
            self.table_listbox.clear()
            self.available_tables = get_tables(cursor)

            if not self.available_tables:
                self.log("Nenhuma tabela encontrada no banco de dados.")
                return

            # Adiciona as tabelas à lista
            for table in self.available_tables:
                self.table_listbox.addItem(table)

            self.log(f"Carregadas {len(self.available_tables)} tabelas. Selecione as tabelas para converter.")
            self.convert_button.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "Erro", f"Erro ao carregar tabelas: {str(e)}")
            self.log(f"Erro ao carregar tabelas: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def start_conversion(self):
        """Inicia o processo de conversão."""
        if not self.input_path or not self.output_path:
            QMessageBox.critical(self, "Erro", "Por favor, selecione os arquivos de entrada e saída.")
            return

        # Obtém as tabelas selecionadas
        selected_items = self.table_listbox.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "Aviso", "Nenhuma tabela selecionada para conversão.")
            return

        selected_tables = [item.text() for item in selected_items]

        # Desabilita o botão e limpa o log
        self.convert_button.setEnabled(False)
        self.convert_button.setText("Convertendo...")
        self.log_text.clear()

        # Cria e inicia o worker thread
        self.conversion_worker = ConversionWorker(self.input_path, self.output_path, selected_tables)
        self.conversion_worker.log_signal.connect(self.log)
        self.conversion_worker.finished_signal.connect(self.on_conversion_finished)
        self.conversion_worker.start()

    def on_conversion_finished(self, success, message):
        """Chamado quando a conversão termina."""
        self.convert_button.setEnabled(True)
        self.convert_button.setText("Iniciar Conversão")

        if success:
            self.log("\nConversão concluída com sucesso!")
            self.log(f"Arquivo SQL gerado em: {os.path.abspath(self.output_path)}")
            QMessageBox.information(self, "Sucesso", message)
        else:
            QMessageBox.critical(self, "Erro na Conversão", message)

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Define o estilo da aplicação
    app.setStyle('Fusion')

    # Cria e mostra a janela principal
    window = ConverterApp()
    window.show()

    # Executa a aplicação
    sys.exit(app.exec())
