// qrawfont.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_RawFont)

class QRawFont
{
%TypeHeaderCode
#include <qrawfont.h>
%End

public:
    enum AntialiasingType
    {
        PixelAntialiasing,
        SubPixelAntialiasing,
    };

    QRawFont();
    QRawFont(const QString &fileName, qreal pixelSize, QFont::HintingPreference hintingPreference = QFont::PreferDefaultHinting);
    QRawFont(const QByteArray &fontData, qreal pixelSize, QFont::HintingPreference hintingPreference = QFont::PreferDefaultHinting);
    QRawFont(const QRawFont &other);
    ~QRawFont();
    bool isValid() const;
    bool operator==(const QRawFont &other) const;
    bool operator!=(const QRawFont &other) const;
    QString familyName() const;
    QString styleName() const;
    QFont::Style style() const;
    int weight() const;
    QList<unsigned int> glyphIndexesForString(const QString &text) const;
    QList<QPointF> advancesForGlyphIndexes(const QList<unsigned int> &glyphIndexes, QRawFont::LayoutFlags layoutFlags) const;
    QList<QPointF> advancesForGlyphIndexes(const QList<unsigned int> &glyphIndexes) const;
    QImage alphaMapForGlyph(quint32 glyphIndex, QRawFont::AntialiasingType antialiasingType = QRawFont::SubPixelAntialiasing, const QTransform &transform = QTransform()) const;
    QPainterPath pathForGlyph(quint32 glyphIndex) const;
    void setPixelSize(qreal pixelSize);
    qreal pixelSize() const;
    QFont::HintingPreference hintingPreference() const;
    qreal ascent() const;
    qreal descent() const;
    qreal leading() const;
    qreal xHeight() const;
    qreal averageCharWidth() const;
    qreal maxCharWidth() const;
    qreal unitsPerEm() const;
    void loadFromFile(const QString &fileName, qreal pixelSize, QFont::HintingPreference hintingPreference) /ReleaseGIL/;
    void loadFromData(const QByteArray &fontData, qreal pixelSize, QFont::HintingPreference hintingPreference) /ReleaseGIL/;
    bool supportsCharacter(uint ucs4) const;
    bool supportsCharacter(QChar character) const;
    QList<QFontDatabase::WritingSystem> supportedWritingSystems() const;
    QByteArray fontTable(const char *tagName) const;
%If (Qt_6_7_0 -)
    QByteArray fontTable(QFont::Tag tag) const;
%End
    static QRawFont fromFont(const QFont &font, QFontDatabase::WritingSystem writingSystem = QFontDatabase::Any);
    QRectF boundingRect(quint32 glyphIndex) const;
    qreal lineThickness() const;
    qreal underlinePosition() const;
    void swap(QRawFont &other /Constrained/);

    enum LayoutFlag /BaseType=Flag/
    {
        SeparateAdvances,
        KernedAdvances,
        UseDesignMetrics,
    };

    typedef QFlags<QRawFont::LayoutFlag> LayoutFlags;
    qreal capHeight() const;
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

%End
