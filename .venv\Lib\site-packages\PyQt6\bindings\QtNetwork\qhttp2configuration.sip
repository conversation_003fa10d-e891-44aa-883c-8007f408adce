// qhttp2configuration.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHttp2Configuration
{
%TypeHeaderCode
#include <qhttp2configuration.h>
%End

public:
    QHttp2Configuration();
    QHttp2Configuration(const QHttp2Configuration &other);
    ~QHttp2Configuration();
    void setServerPushEnabled(bool enable);
    bool serverPushEnabled() const;
    void setHuffmanCompressionEnabled(bool enable);
    bool huffmanCompressionEnabled() const;
    bool setSessionReceiveWindowSize(unsigned int size);
    unsigned int sessionReceiveWindowSize() const;
    bool setStreamReceiveWindowSize(unsigned int size);
    unsigned int streamReceiveWindowSize() const;
    bool setMaxFrameSize(unsigned int size);
    unsigned int maxFrameSize() const;
    void swap(QHttp2Configuration &other /Constrained/);
%If (Qt_6_9_0 -)
    void setMaxConcurrentStreams(unsigned int value);
%End
%If (Qt_6_9_0 -)
    unsigned int maxConcurrentStreams() const;
%End
};

bool operator==(const QHttp2Configuration &lhs, const QHttp2Configuration &rhs);
bool operator!=(const QHttp2Configuration &lhs, const QHttp2Configuration &rhs);
