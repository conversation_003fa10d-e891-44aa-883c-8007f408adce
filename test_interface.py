#!/usr/bin/env python3
"""
Teste simples para verificar se a interface PyQt6 carrega corretamente.
"""

import sys
import os

# Adiciona o diretório atual ao path para importar o módulo
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt6.QtWidgets import QApplication
    from mdb_to_postgres import ConverterApp
    
    def test_interface():
        """Testa se a interface pode ser criada sem erros."""
        app = QApplication(sys.argv)
        
        # Cria a janela principal
        window = ConverterApp()
        
        # Verifica se a janela foi criada corretamente
        assert window.windowTitle() == "Conversor MDB/ACCDB para PostgreSQL"
        assert window.input_entry is not None
        assert window.output_entry is not None
        assert window.table_listbox is not None
        assert window.log_text is not None
        assert window.convert_button is not None
        
        print("✓ Interface criada com sucesso!")
        print("✓ Todos os widgets principais estão presentes")
        print("✓ Teste da interface passou!")
        
        return True
        
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    print("Certifique-se de que o PyQt6 está instalado: pip install PyQt6")
    return False
except Exception as e:
    print(f"❌ Erro ao testar interface: {e}")
    return False

if __name__ == "__main__":
    success = test_interface()
    sys.exit(0 if success else 1)
