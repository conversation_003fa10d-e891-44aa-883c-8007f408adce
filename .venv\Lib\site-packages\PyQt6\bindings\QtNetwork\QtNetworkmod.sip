// QtNetworkmod.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt6.QtNetwork, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip

%Copying
Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt6.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype PyQt6.sip.simplewrapper

%Include qabstractnetworkcache.sip
%Include qabstractsocket.sip
%Include qauthenticator.sip
%Include qdnslookup.sip
%Include qformdatabuilder.sip
%Include qhostaddress.sip
%Include qhostinfo.sip
%Include qhstspolicy.sip
%Include qhttp1configuration.sip
%Include qhttp2configuration.sip
%Include qhttpheaders.sip
%Include qhttpmultipart.sip
%Include qlocalserver.sip
%Include qlocalsocket.sip
%Include qnetworkaccessmanager.sip
%Include qnetworkcookie.sip
%Include qnetworkcookiejar.sip
%Include qnetworkdatagram.sip
%Include qnetworkdiskcache.sip
%Include qnetworkinformation.sip
%Include qnetworkinterface.sip
%Include qnetworkproxy.sip
%Include qnetworkreply.sip
%Include qnetworkrequest.sip
%Include qnetworkrequestfactory.sip
%Include qocspresponse.sip
%Include qpassworddigestor.sip
%Include qrestaccessmanager.sip
%Include qrestreply.sip
%Include qssl.sip
%Include qsslcertificate.sip
%Include qsslcertificateextension.sip
%Include qsslcipher.sip
%Include qsslconfiguration.sip
%Include qssldiffiehellmanparameters.sip
%Include qsslellipticcurve.sip
%Include qsslerror.sip
%Include qsslkey.sip
%Include qsslpresharedkeyauthenticator.sip
%Include qsslserver.sip
%Include qsslsocket.sip
%Include qtcpserver.sip
%Include qtcpsocket.sip
%Include qudpsocket.sip
%Include qpynetwork_qhash.sip
%Include qpynetwork_qlist.sip
%Include qpynetwork_qmap.sip
