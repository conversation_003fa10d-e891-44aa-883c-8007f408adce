# Conversor MDB/ACCDB para PostgreSQL

Uma aplicação moderna com interface PyQt6 para converter bancos de dados Microsoft Access (MDB/ACCDB) para scripts SQL compatíveis com PostgreSQL.

## 🚀 Características

- **Interface Moderna**: Interface gráfica moderna construída com PyQt6
- **Conversão Completa**: Converte estruturas de tabelas e dados
- **Seleção de Tabelas**: Permite selecionar quais tabelas converter
- **Log em Tempo Real**: Acompanhe o progresso da conversão
- **Processamento Assíncrono**: Interface responsiva durante a conversão
- **Normalização Automática**: Nomes de tabelas e campos são automaticamente normalizados

## 📋 Pré-requisitos

### Software Necessário

1. **Python 3.7+**
2. **Microsoft Access Database Engine** (32 ou 64 bits, conforme sua instalação do Python)
   - Download: [Microsoft Access Database Engine](https://www.microsoft.com/en-us/download/details.aspx?id=54920)

### Dependências Python

```bash
pip install -r requirements.txt
```

As dependências incluem:
- `pyodbc>=5.1.0` - Para conectar com bancos Access
- `PyQt6>=6.4.0` - Para a interface gráfica moderna

## 🛠️ Instalação

1. **Clone ou baixe o projeto**
2. **Instale as dependências**:
   ```bash
   pip install -r requirements.txt
   ```
3. **Execute a aplicação**:
   ```bash
   python mdb_to_postgres.py
   ```

## 📖 Como Usar

### 1. Seleção do Arquivo
- Clique em "Procurar..." para selecionar seu arquivo MDB/ACCDB
- O nome do arquivo SQL de saída será gerado automaticamente

### 2. Carregamento das Tabelas
- Clique em "Carregar Tabelas" para listar todas as tabelas disponíveis
- Selecione as tabelas que deseja converter (use Ctrl+clique para múltiplas seleções)

### 3. Conversão
- Clique em "Iniciar Conversão" para começar o processo
- Acompanhe o progresso no log
- O arquivo SQL será salvo no local especificado

## 🔧 Funcionalidades Técnicas

### Normalização de Nomes
- Remove acentos e caracteres especiais
- Converte espaços para underscores
- Remove prefixos "cadastro_" automaticamente
- Garante compatibilidade com PostgreSQL

### Mapeamento de Tipos
- **Texto**: VARCHAR, LONGCHAR, TEXT, MEMO → text/varchar
- **Numéricos**: INTEGER, DOUBLE, CURRENCY → numeric(10,2)/integer
- **Datas**: DATETIME, DATE → date
- **Booleanos**: YESNO, BIT → boolean
- **Binários**: BINARY, OLEOBJECT → bytea

### Tratamento Especial
- **Primeira coluna**: Automaticamente convertida para `serial PRIMARY KEY`
- **Campos "quantidade"**: Convertidos para `numeric(10,2)`
- **Campos "hora/horario"**: Convertidos para `time`

## 🎨 Interface Moderna

A nova interface PyQt6 oferece:
- **Design Responsivo**: Layout que se adapta ao tamanho da janela
- **Estilos Modernos**: Botões e campos com visual contemporâneo
- **Feedback Visual**: Estados visuais para botões e campos
- **Log Estilizado**: Área de log com fonte monoespaçada para melhor legibilidade

## 🧪 Testes

Execute o teste da interface:
```bash
python test_interface.py
```

## 📝 Estrutura do Projeto

```
mdb-to-postgresql/
├── mdb_to_postgres.py    # Aplicação principal
├── requirements.txt      # Dependências
├── test_interface.py     # Teste da interface
├── README.md            # Documentação
└── DADOS.MDB           # Arquivo de exemplo (se presente)
```

## 🔍 Solução de Problemas

### Erro: "Nenhum driver ODBC do Microsoft Access foi encontrado"
- Instale o Microsoft Access Database Engine
- Certifique-se de usar a versão (32/64 bits) compatível com seu Python

### Erro: "A biblioteca 'pyodbc' não está instalada"
```bash
pip install pyodbc
```

### Erro: "No module named 'PyQt6'"
```bash
pip install PyQt6
```

## 📄 Licença

Este projeto é de código aberto. Sinta-se livre para usar, modificar e distribuir.

## 🤝 Contribuições

Contribuições são bem-vindas! Sinta-se à vontade para:
- Reportar bugs
- Sugerir melhorias
- Enviar pull requests

## 📞 Suporte

Para dúvidas ou problemas, verifique:
1. Esta documentação
2. Os logs de erro da aplicação
3. A seção de solução de problemas acima
