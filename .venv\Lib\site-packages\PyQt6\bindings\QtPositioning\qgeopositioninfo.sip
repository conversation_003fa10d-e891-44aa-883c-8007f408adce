// qgeopositioninfo.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QGeoPositionInfo
{
%TypeHeaderCode
#include <qgeopositioninfo.h>
%End

public:
    enum Attribute
    {
        Direction,
        GroundSpeed,
        VerticalSpeed,
        MagneticVariation,
        HorizontalAccuracy,
        VerticalAccuracy,
%If (Qt_6_3_0 -)
        DirectionAccuracy,
%End
    };

    QGeoPositionInfo();
    QGeoPositionInfo(const QGeoCoordinate &coordinate, const QDateTime &updateTime);
    QGeoPositionInfo(const QGeoPositionInfo &other);
    ~QGeoPositionInfo();
    bool isValid() const;
    void setTimestamp(const QDateTime &timestamp);
    QDateTime timestamp() const;
    void setCoordinate(const QGeoCoordinate &coordinate);
    QGeoCoordinate coordinate() const;
    void setAttribute(QGeoPositionInfo::Attribute attribute, qreal value);
    qreal attribute(QGeoPositionInfo::Attribute attribute) const;
    void removeAttribute(QGeoPositionInfo::Attribute attribute);
    bool hasAttribute(QGeoPositionInfo::Attribute attribute) const;
    void swap(QGeoPositionInfo &other /Constrained/);
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

%End
%If (Qt_6_2_0 -)
QDataStream &operator<<(QDataStream &stream, const QGeoPositionInfo &info);
%End
%If (Qt_6_2_0 -)
QDataStream &operator>>(QDataStream &stream, QGeoPositionInfo &info /Constrained/);
%End
%If (Qt_6_2_0 -)
bool operator==(const QGeoPositionInfo &lhs, const QGeoPositionInfo &rhs);
%End
%If (Qt_6_2_0 -)
bool operator!=(const QGeoPositionInfo &lhs, const QGeoPositionInfo &rhs);
%End
